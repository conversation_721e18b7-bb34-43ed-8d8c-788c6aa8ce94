import { useState } from 'react';
import { Link, useNavigate } from '@tanstack/react-router';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../contexts/AuthContext';
import { useCertificate } from '../contexts/CertificateContext';
import {
  DocumentTextIcon as FileText,
  HomeIcon as Home,
  BuildingOfficeIcon as Building,
  ArrowLeftIcon as ArrowLeft,
  ArrowPathIcon as RefreshCw,
  QuestionMarkCircleIcon as HelpCircle
} from '@heroicons/react/24/outline';

// Define certificate types
type CertificateType = 'WG/V' | 'WG/B' | 'NWG/V';

// Certificate type quiz component
const CertificateTypeQuiz = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { user, signInAnonymously } = useAuth();
  const { createNewCertificate } = useCertificate();

  const [step, setStep] = useState(1);
  const [answers, setAnswers] = useState({
    buildingType: "",
    purpose: "",
    units: "",
    buildingYear: "",
    renovated: "",
    consumptionData: ""
  });
  const [result, setResult] = useState<CertificateType | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Function to determine certificate type based on answers
  const determineCertificateType = (answersData: {
    buildingType: string;
    purpose: string;
    units: string;
    buildingYear: string;
    renovated: string;
    consumptionData: string;
  }): CertificateType => {
    // Non-residential buildings always get NWG/V
    if (answersData.buildingType === "nichtWohn") {
      return "NWG/V";
    }

    // For residential buildings, determine between WG/V and WG/B
    // WG/B is required for:
    // - New construction/major renovation
    // - Buildings built before 1977 that haven't been renovated according to WSchV 77
    if (answersData.purpose === "neubau") {
      return "WG/B";
    }

    if (answersData.buildingYear === "vor1977" && answersData.renovated === "nein") {
      return "WG/B";
    }

    // For all other residential cases, WG/V is allowed (and often preferred due to lower cost)
    return "WG/V";
  };

  // Function to handle answers and navigate through quiz
  const handleAnswer = (question: string, answer: string) => {
    const newAnswers = { ...answers, [question]: answer };
    setAnswers(newAnswers);

    if (question === "buildingType") {
      if (answer === "nichtWohn") {
        // Non-residential buildings go directly to result
        const certificateType = determineCertificateType({ ...newAnswers });
        setResult(certificateType);
        setStep(7);
      } else {
        setStep(2);
      }
    } else if (question === "purpose") {
      if (answer === "neubau") {
        // New construction goes directly to result
        const certificateType = determineCertificateType({ ...newAnswers });
        setResult(certificateType);
        setStep(7);
      } else {
        setStep(3);
      }
    } else if (question === "units") {
      setStep(4);
    } else if (question === "buildingYear") {
      if (answer === "nach1977") {
        setStep(6);
      } else {
        setStep(5);
      }
    } else if (question === "renovated") {
      if (answer === "nein") {
        // Not renovated according to WSchV 77 -> WG/B required
        const certificateType = determineCertificateType({ ...newAnswers });
        setResult(certificateType);
        setStep(7);
      } else {
        setStep(6);
      }
    } else if (question === "consumptionData") {
      // Final step - determine result
      const certificateType = determineCertificateType({ ...newAnswers });
      setResult(certificateType);
      setStep(7);
    }
  };

  // Function to create certificate and navigate
  const handleCreateCertificate = async () => {
    if (!result) return;

    try {
      setIsCreating(true);
      setError(null);

      // If user is not authenticated, sign in anonymously first
      if (!user) {
        const { error: authError } = await signInAnonymously();
        if (authError) {
          throw new Error(`Fehler bei der anonymen Anmeldung: ${authError.message}`);
        }
        // Wait a moment for the auth state to update
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Create a new certificate with the determined type
      await createNewCertificate(result);

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['energieausweise'] });

      // Navigate to the first data entry page
      navigate({ to: '/erfassen/objektdaten' });
    } catch (err: any) {
      setError(`Fehler beim Erstellen: ${err.message}`);
    } finally {
      setIsCreating(false);
    }
  };

  // Function to reset the quiz
  const resetQuiz = () => {
    setAnswers({
      buildingType: "",
      purpose: "",
      units: "",
      buildingYear: "",
      renovated: "",
      consumptionData: ""
    });
    setResult(null);
    setStep(1);
    setError(null);
  };

  return (
    <div className="max-w-3xl mx-auto mt-16">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-4">
          Welcher Energieausweis ist der richtige für Sie?
        </h2>
        <p className="text-gray-600">
          Beantworten Sie ein paar kurze Fragen und wir empfehlen Ihnen den passenden Energieausweistyp.
        </p>
      </div>

      {/* Progress bar */}
      {step < 7 && (
        <div className="mb-8">
          <div className="flex justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Schritt {step} von 6</span>
            <span className="text-sm font-medium text-gray-700">{Math.round((step / 6) * 100)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div
              className="bg-green-600 h-2.5 rounded-full transition-all duration-300"
              style={{ width: `${(step / 6) * 100}%` }}
            ></div>
          </div>
        </div>
      )}

      {/* Quiz questions */}
      {step === 1 && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold mb-6 text-center">Um welche Art von Gebäude handelt es sich?</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={() => handleAnswer("buildingType", "wohn")}
              className="bg-green-50 hover:bg-green-100 p-4 rounded-lg border-2 border-green-200 transition-colors flex flex-col items-center text-center"
            >
              <div className="bg-green-100 p-3 rounded-full mb-3">
                <Home className="h-6 w-6 text-green-600" />
              </div>
              <span className="font-semibold text-green-800">Wohngebäude</span>
              <p className="text-sm text-gray-600 mt-2">Ein- oder Mehrfamilienhaus, Wohnung</p>
            </button>

            <button
              onClick={() => handleAnswer("buildingType", "nichtWohn")}
              className="bg-green-50 hover:bg-green-100 p-4 rounded-lg border-2 border-green-200 transition-colors flex flex-col items-center text-center"
            >
              <div className="bg-green-100 p-3 rounded-full mb-3">
                <Building className="h-6 w-6 text-green-600" />
              </div>
              <span className="font-semibold text-green-800">Nicht-Wohngebäude</span>
              <p className="text-sm text-gray-600 mt-2">Büro, Geschäft, Gewerbe, öffentliches Gebäude</p>
            </button>
          </div>
        </div>
      )}

      {step === 2 && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold mb-6 text-center">Wofür benötigen Sie den Energieausweis?</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={() => handleAnswer("purpose", "vermietung")}
              className="bg-green-50 hover:bg-green-100 p-4 rounded-lg border-2 border-green-200 transition-colors flex flex-col items-center text-center"
            >
              <div className="bg-green-100 p-3 rounded-full mb-3">
                <FileText className="h-6 w-6 text-green-600" />
              </div>
              <span className="font-semibold text-green-800">Vermietung / Verkauf</span>
              <p className="text-sm text-gray-600 mt-2">Für bestehende Gebäude, die vermietet oder verkauft werden sollen</p>
            </button>

            <button
              onClick={() => handleAnswer("purpose", "neubau")}
              className="bg-green-50 hover:bg-green-100 p-4 rounded-lg border-2 border-green-200 transition-colors flex flex-col items-center text-center"
            >
              <div className="bg-green-100 p-3 rounded-full mb-3">
                <Home className="h-6 w-6 text-green-600" />
              </div>
              <span className="font-semibold text-green-800">Neubau / Modernisierung</span>
              <p className="text-sm text-gray-600 mt-2">Für Neubauten oder umfassend modernisierte Gebäude</p>
            </button>
          </div>

          <div className="mt-6 flex justify-center">
            <button
              onClick={() => setStep(1)}
              className="flex items-center text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Zurück
            </button>
          </div>
        </div>
      )}

      {step === 3 && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold mb-6 text-center">Wie viele Wohneinheiten hat Ihr Gebäude?</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={() => handleAnswer("units", "1bis4")}
              className="bg-green-50 hover:bg-green-100 p-4 rounded-lg border-2 border-green-200 transition-colors flex flex-col items-center text-center"
            >
              <span className="font-semibold text-green-800 text-xl">1-4 Wohneinheiten</span>
              <p className="text-sm text-gray-600 mt-2">Ein- bis Vierfamilienhaus</p>
            </button>

            <button
              onClick={() => handleAnswer("units", "ab5")}
              className="bg-green-50 hover:bg-green-100 p-4 rounded-lg border-2 border-green-200 transition-colors flex flex-col items-center text-center"
            >
              <span className="font-semibold text-green-800 text-xl">ab 5 Wohneinheiten</span>
              <p className="text-sm text-gray-600 mt-2">Mehrfamilienhaus oder größeres Gebäude</p>
            </button>
          </div>

          <div className="mt-6 flex justify-center">
            <button
              onClick={() => setStep(2)}
              className="flex items-center text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Zurück
            </button>
          </div>
        </div>
      )}

      {step === 4 && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold mb-6 text-center">Wann wurde Ihr Gebäude gebaut?</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={() => handleAnswer("buildingYear", "vor1977")}
              className="bg-green-50 hover:bg-green-100 p-4 rounded-lg border-2 border-green-200 transition-colors flex flex-col items-center text-center"
            >
              <span className="font-semibold text-green-800 text-xl">Vor dem 1.11.1977</span>
              <p className="text-sm text-gray-600 mt-2">Bauantrag vor der ersten Wärmeschutzverordnung</p>
            </button>

            <button
              onClick={() => handleAnswer("buildingYear", "nach1977")}
              className="bg-green-50 hover:bg-green-100 p-4 rounded-lg border-2 border-green-200 transition-colors flex flex-col items-center text-center"
            >
              <span className="font-semibold text-green-800 text-xl">Nach dem 1.11.1977</span>
              <p className="text-sm text-gray-600 mt-2">Bauantrag nach der ersten Wärmeschutzverordnung</p>
            </button>
          </div>

          <div className="mt-6 flex justify-center">
            <button
              onClick={() => setStep(3)}
              className="flex items-center text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Zurück
            </button>
          </div>
        </div>
      )}

      {step === 5 && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold mb-6 text-center">Wurde Ihr Gebäude nach der Wärmeschutzverordnung von 1977 saniert?</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={() => handleAnswer("renovated", "ja")}
              className="bg-green-50 hover:bg-green-100 p-4 rounded-lg border-2 border-green-200 transition-colors flex flex-col items-center text-center"
            >
              <span className="font-semibold text-green-800 text-xl">Ja</span>
              <p className="text-sm text-gray-600 mt-2">Das Gebäude wurde gemäß WSchV 77 oder höher saniert</p>
            </button>

            <button
              onClick={() => handleAnswer("renovated", "nein")}
              className="bg-green-50 hover:bg-green-100 p-4 rounded-lg border-2 border-green-200 transition-colors flex flex-col items-center text-center"
            >
              <span className="font-semibold text-green-800 text-xl">Nein</span>
              <p className="text-sm text-gray-600 mt-2">Das Gebäude wurde nicht oder nicht nach WSchV 77 saniert</p>
            </button>
          </div>

          <div className="mt-6 flex justify-center">
            <button
              onClick={() => setStep(4)}
              className="flex items-center text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Zurück
            </button>
          </div>
        </div>
      )}

      {step === 6 && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-xl font-semibold mb-6 text-center">Sind Verbrauchswerte für die letzten drei Jahre vorhanden?</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={() => handleAnswer("consumptionData", "ja")}
              className="bg-green-50 hover:bg-green-100 p-4 rounded-lg border-2 border-green-200 transition-colors flex flex-col items-center text-center"
            >
              <span className="font-semibold text-green-800 text-xl">Ja</span>
              <p className="text-sm text-gray-600 mt-2">Verbrauchsdaten für Heizung und Warmwasser sind vorhanden</p>
            </button>

            <button
              onClick={() => handleAnswer("consumptionData", "nein")}
              className="bg-green-50 hover:bg-green-100 p-4 rounded-lg border-2 border-green-200 transition-colors flex flex-col items-center text-center"
            >
              <span className="font-semibold text-green-800 text-xl">Nein</span>
              <p className="text-sm text-gray-600 mt-2">Keine vollständigen Verbrauchsdaten vorhanden</p>
            </button>
          </div>

          <div className="mt-6 flex justify-center">
            <button
              onClick={() => {
                if (answers.buildingYear === "nach1977") {
                  setStep(4);
                } else {
                  setStep(5);
                }
              }}
              className="flex items-center text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Zurück
            </button>
          </div>
        </div>
      )}

      {step === 7 && result && (
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-semibold mb-4">Ihr Ergebnis</h3>
            <div className="bg-green-100 p-6 rounded-lg inline-block">
              <h4 className="text-xl font-bold text-green-800 mb-2">
                {result === 'WG/V' && 'Wohngebäude-Verbrauchsausweis'}
                {result === 'WG/B' && 'Wohngebäude-Bedarfsausweis'}
                {result === 'NWG/V' && 'Nicht-Wohngebäude-Verbrauchsausweis'}
              </h4>
              <p className="text-gray-700">
                {result === 'WG/V' && 'Für Ihr Wohngebäude empfehlen wir einen Verbrauchsausweis basierend auf dem tatsächlichen Energieverbrauch.'}
                {result === 'WG/B' && 'Für Ihr Wohngebäude ist ein Bedarfsausweis basierend auf dem berechneten Energiebedarf erforderlich.'}
                {result === 'NWG/V' && 'Für Ihr Nicht-Wohngebäude empfehlen wir einen Verbrauchsausweis basierend auf dem tatsächlichen Energieverbrauch.'}
              </p>
            </div>
          </div>

          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700">{error}</p>
            </div>
          )}

          <div className="mt-8 space-y-4">
            <h4 className="font-semibold text-gray-800">Ihre Angaben:</h4>
            <ul className="space-y-2 text-gray-700">
              <li>
                <span className="font-medium">Gebäudeart:</span> {answers.buildingType === "wohn" ? "Wohngebäude" : "Nicht-Wohngebäude"}
              </li>
              {answers.purpose && (
                <li>
                  <span className="font-medium">Zweck:</span> {answers.purpose === "vermietung" ? "Vermietung/Verkauf" : "Neubau/Modernisierung"}
                </li>
              )}
              {answers.units && (
                <li>
                  <span className="font-medium">Wohneinheiten:</span> {answers.units === "1bis4" ? "1-4 Wohneinheiten" : "ab 5 Wohneinheiten"}
                </li>
              )}
              {answers.buildingYear && (
                <li>
                  <span className="font-medium">Baujahr:</span> {answers.buildingYear === "vor1977" ? "vor dem 1.11.1977" : "nach dem 1.11.1977"}
                </li>
              )}
              {answers.renovated && (
                <li>
                  <span className="font-medium">Nach WSchV 77 saniert:</span> {answers.renovated === "ja" ? "Ja" : "Nein"}
                </li>
              )}
              {answers.consumptionData && (
                <li>
                  <span className="font-medium">Verbrauchswerte vorhanden:</span> {answers.consumptionData === "ja" ? "Ja" : "Nein"}
                </li>
              )}
            </ul>
          </div>

          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={handleCreateCertificate}
              disabled={isCreating}
              className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              {isCreating ? 'Erstelle...' : 'Energieausweis jetzt erstellen'}
            </button>
            <button
              onClick={resetQuiz}
              className="flex items-center justify-center gap-2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-3 px-6 rounded-lg transition-colors"
            >
              <RefreshCw className="h-4 w-4" />
              Neu starten
            </button>
          </div>
        </div>
      )}

      {/* Help section */}
      <div className="mt-8 bg-green-50 p-4 rounded-lg border border-green-200 flex items-start">
        <HelpCircle className="h-5 w-5 text-green-500 mr-3 flex-shrink-0 mt-0.5" />
        <div>
          <h4 className="font-semibold text-green-800 mb-1">Benötigen Sie Hilfe?</h4>
          <p className="text-sm text-gray-700">
            Wenn Sie unsicher sind, welcher Energieausweis für Ihre Immobilie der richtige ist,
            können Sie auch direkt den Typ auswählen oder uns kontaktieren.
          </p>
          <div className="mt-2 flex flex-wrap gap-2">
            <Link to="/erfassen/certificate-type" className="text-sm text-green-600 hover:text-green-800 font-medium">
              Typ manuell auswählen
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export const HomePage = () => {
  const { user, isAnonymous } = useAuth();

  return (
    <div className="max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-800 mb-6">
        Willkommen bei der Energieausweis App
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        Erstellen Sie einfach und schnell einen Energieausweis für Ihr Gebäude.
        Geben Sie Ihre Daten ein, erhalten Sie eine Zusammenfassung und kaufen Sie
        Ihren Energieausweis online.
      </p>
      <div className="flex flex-col sm:flex-row gap-4">
        {user ? (
          <>
            <Link
              to="/erfassen/certificate-type"
              className="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg text-center transition-colors"
            >
              Neuen Energieausweis erstellen
            </Link>
            <Link
              to="/meine-zertifikate"
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-3 px-6 rounded-lg text-center transition-colors"
            >
              Meine Zertifikate verwalten
            </Link>
          </>
        ) : (
          <>
            <Link
              to="/erfassen/certificate-type"
              className="bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg text-center transition-colors"
            >
              Energieausweis erstellen
            </Link>
            <Link
              to="/login"
              className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-3 px-6 rounded-lg text-center transition-colors"
            >
              Anmelden
            </Link>
          </>
        )}
      </div>

      {/* Certificate Type Quiz */}
      <CertificateTypeQuiz />
    </div>
  );
};
